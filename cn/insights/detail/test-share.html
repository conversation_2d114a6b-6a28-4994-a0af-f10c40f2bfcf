<!DOCTYPE html>
<html lang="zh-HANS">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>分享链接动态更新测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-controls {
            background: #f5f5f5;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        .test-controls button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-controls button:hover {
            background: #005a87;
        }
        .share-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .share-links {
            list-style: none;
            padding: 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .share-links a {
            display: inline-block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            border-radius: 3px;
            font-size: 14px;
            transition: opacity 0.2s;
        }
        .share-links a:hover {
            opacity: 0.8;
        }
        #linkedin-share { background: #0077b5; }
        #twitter-share { background: #1da1f2; }
        #facebook-share { background: #1877f2; }
        #copy-link { background: #6c757d; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .current-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .current-info h4 {
            margin-top: 0;
            color: #495057;
        }
        .current-info p {
            margin: 5px 0;
            font-family: monospace;
            background: white;
            padding: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>分享链接动态更新测试</h1>
    
    <div class="test-controls">
        <h2>测试不同新闻ID的分享链接</h2>
        <p>点击下面的按钮测试不同新闻的分享链接更新：</p>
        <button onclick="loadNews('1')">加载新闻1 (数据安全指南)</button>
        <button onclick="loadNews('2')">加载新闻2 (信息治理报告)</button>
        <button onclick="loadNews('123')">加载新闻123 (自定义ID)</button>
        <div id="status"></div>
    </div>

    <div class="current-info">
        <h4>当前页面信息</h4>
        <p><strong>新闻ID:</strong> <span id="current-id">1</span></p>
        <p><strong>文章标题:</strong> <span id="current-title">默认标题</span></p>
        <p><strong>分享URL:</strong> <span id="current-url">https://www.crownrms.com/cn/insights/detail/index.html?id=1</span></p>
    </div>

    <!-- 分享链接区域 -->
    <section class="share-section">
        <div class="article-share-wrap">
            <h3>分享这篇文章</h3>
            <ul class="share-links">
                <li>
                    <a id="linkedin-share" href="https://www.linkedin.com/shareArticle?mini=true&url=https://www.crownrms.com/cn/insights/detail/index.html?id=1" target="_blank">
                        LinkedIn 分享
                    </a>
                </li>
                <li>
                    <a id="twitter-share" href="https://x.com/intent/post?url=https://www.crownrms.com/cn/insights/detail/index.html?id=1&text=文章标题" target="_blank">
                        Twitter/X 分享
                    </a>
                </li>
                <li>
                    <a id="facebook-share" href="https://www.facebook.com/sharer/sharer.php?u=https://www.crownrms.com/cn/insights/detail/index.html?id=1" target="_blank">
                        Facebook 分享
                    </a>
                </li>
                <li>
                    <a id="copy-link" href="https://www.crownrms.com/cn/insights/detail/index.html?id=1" class="copy-to-clipboard">
                        复制链接
                    </a>
                </li>
            </ul>
        </div>
    </section>

    <div class="current-info">
        <h4>实际链接地址（点击上方按钮后会动态更新）</h4>
        <p><strong>LinkedIn:</strong> <span id="linkedin-url">-</span></p>
        <p><strong>Twitter:</strong> <span id="twitter-url">-</span></p>
        <p><strong>Facebook:</strong> <span id="facebook-url">-</span></p>
        <p><strong>复制链接:</strong> <span id="copy-url">-</span></p>
    </div>

    <!-- 引入模拟API数据 -->
    <script src="mock-api.js"></script>
    <!-- 引入动态数据加载脚本 -->
    <script src="index.js"></script>

    <script>
        // 更新显示的链接信息
        function updateDisplayedLinks() {
            const linkedinShare = document.querySelector('#linkedin-share');
            const twitterShare = document.querySelector('#twitter-share');
            const facebookShare = document.querySelector('#facebook-share');
            const copyLink = document.querySelector('#copy-link');

            document.getElementById('linkedin-url').textContent = linkedinShare ? linkedinShare.href : '-';
            document.getElementById('twitter-url').textContent = twitterShare ? twitterShare.href : '-';
            document.getElementById('facebook-url').textContent = facebookShare ? facebookShare.href : '-';
            document.getElementById('copy-url').textContent = copyLink ? copyLink.href : '-';
        }

        // 测试函数
        function loadNews(newsId) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<div class="status">正在加载数据...</div>';
            
            // 更新URL参数以模拟不同的新闻ID
            const url = new URL(window.location);
            url.searchParams.set('id', newsId);
            window.history.replaceState({}, '', url);
            
            // 调用数据加载函数
            getNewsDetail(newsId)
                .then(newsData => {
                    if (newsData && Object.keys(newsData).length > 0) {
                        // 更新分享链接
                        updateShareLinks(newsId, newsData);
                        
                        // 更新显示信息
                        document.getElementById('current-id').textContent = newsId;
                        document.getElementById('current-title').textContent = newsData.title || '默认标题';
                        document.getElementById('current-url').textContent = `https://www.crownrms.com/cn/insights/detail/index.html?id=${newsId}`;
                        
                        // 更新显示的链接
                        updateDisplayedLinks();
                        
                        statusDiv.innerHTML = `<div class="status success">✓ 分享链接已更新！新闻ID: ${newsId}</div>`;
                        console.log('分享链接已更新:', newsData);
                    } else {
                        statusDiv.innerHTML = `<div class="status error">✗ 未获取到新闻数据，新闻ID: ${newsId}</div>`;
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = `<div class="status error">✗ 加载失败: ${error.message}</div>`;
                    console.error('加载新闻详情失败:', error);
                });
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            const statusDiv = document.getElementById('status');
            const mode = CONFIG.isDevelopment ? '开发模式（使用模拟数据）' : '生产模式（使用真实API）';
            statusDiv.innerHTML = `<div class="status">当前模式: ${mode}</div>`;
            
            // 显示初始链接
            updateDisplayedLinks();
        });
    </script>
</body>
</html>
