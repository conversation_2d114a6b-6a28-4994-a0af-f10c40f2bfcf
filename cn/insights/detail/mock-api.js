// 模拟API数据，用于测试动态加载功能
const mockNewsData = {
  1: {
    title: '2024企业数据安全管理指南',
    publishTime: '已发布',
    publishDate: '2024-03-15T08:00:00Z',
    content: `在数字化转型的浪潮中，企业面临着前所未有的数据安全挑战。本指南将为您提供全面的数据安全管理策略。

我们汇集了行业专家的最佳实践，涵盖数据分类、访问控制、加密技术等关键领域。

通过实施这些策略，您的企业将能够有效防范数据泄露风险，确保业务连续性。

立即下载完整的数据安全管理指南，保护您的企业数字资产。`,
    featuredImage:
      '../../wp-content/uploads/sites/7/2025/02/data-security-guide.jpg',
    categories: '数据安全,企业管理',
    downloadTitle: '立即下载指南',
    allowDownloadFile: 'Y'
  },
  2: {
    title: '2025年信息治理趋势报告',
    publishTime: '最新发布',
    publishDate: '2024-12-01T10:30:00Z',
    content: `随着法规要求的不断演进，信息治理已成为企业合规的核心要素。本报告深入分析了2025年的关键趋势。

我们调研了全球500强企业的信息治理实践，总结出最具前瞻性的策略和工具。

报告涵盖了人工智能在信息治理中的应用、隐私保护新标准、以及跨境数据流动的合规要求。

获取这份权威报告，让您的企业在信息治理领域保持领先优势。`,
    featuredImage:
      '../../wp-content/uploads/sites/7/2025/02/governance-trends.jpg',
    categories: '信息治理,合规管理',
    downloadTitle: '获取趋势报告',
    allowDownloadFile: 'Y'
  },
  123: {
    title: '自定义测试文章',
    publishTime: '测试发布',
    publishDate: '2024-06-22T12:00:00Z',
    content: `这是一个自定义的测试文章，用于验证分享链接的动态更新功能。

文章ID为123，可以测试不同ID下的分享链接是否正确更新。

每个社交媒体平台的分享链接都应该包含正确的URL参数和文章标题。`,
    featuredImage:
      '../../wp-content/uploads/sites/7/2025/02/test-image.jpg',
    categories: '测试分类',
    downloadTitle: '测试下载',
    allowDownloadFile: 'N'
  }
}

// 模拟新闻主题数据
const mockTopicData = [
  { typeValue: '数据安全', typeLabel: '数据安全' },
  { typeValue: '企业管理', typeLabel: '企业管理' },
  { typeValue: '信息治理', typeLabel: '信息治理' },
  { typeValue: '合规管理', typeLabel: '合规管理' },
  { typeValue: '测试分类', typeLabel: '测试分类' }
]

// 模拟API响应函数
function mockApiResponse(newsId) {
  return new Promise((resolve) => {
    // 模拟网络延迟
    setTimeout(() => {
      const newsData = mockNewsData[newsId] || mockNewsData[1]
      resolve({
        success: true,
        data: newsData
      })
    }, 500)
  })
}

// 模拟主题API响应函数
function mockTopicApiResponse() {
  return new Promise((resolve) => {
    // 模拟网络延迟
    setTimeout(() => {
      resolve({
        success: true,
        data: mockTopicData
      })
    }, 300)
  })
}

// 如果在浏览器环境中，将模拟函数添加到全局对象
if (typeof window !== 'undefined') {
  window.mockApiResponse = mockApiResponse
  window.mockTopicApiResponse = mockTopicApiResponse
}

// 如果在Node.js环境中，导出模拟函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    mockApiResponse, 
    mockTopicApiResponse, 
    mockNewsData, 
    mockTopicData 
  }
}
