<!DOCTYPE html>
<html lang="zh-HANS">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>搜索功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .search-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .facetwp-facet {
            margin-bottom: 20px;
        }
        .facetwp-input-wrap {
            position: relative;
            display: inline-block;
        }
        .facetwp-search {
            padding: 10px 40px 10px 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            width: 300px;
        }
        .facetwp-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            background: #007cba;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 14px;
        }
        .facetwp-icon:hover {
            background: #005a87;
        }
        .results-section {
            margin-top: 30px;
        }
        .article-item {
            border: 1px solid #ddd;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 5px;
            background: white;
        }
        .article-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .article-topic {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .pagination {
            margin-top: 30px;
            text-align: center;
        }
        .facetwp-pager a {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 2px;
            border: 1px solid #ddd;
            text-decoration: none;
            color: #333;
            border-radius: 3px;
        }
        .facetwp-pager a:hover {
            background: #f0f0f0;
        }
        .facetwp-pager a.active {
            background: #007cba;
            color: white;
            border-color: #007cba;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>搜索功能测试页面</h1>
        
        <div class="search-section">
            <h2>搜索文章</h2>
            <p>在下面的搜索框中输入关键词，然后按回车键或点击搜索图标进行搜索。</p>
            
            <!-- 搜索框 -->
            <div class="facetwp-facet facetwp-facet-search facetwp-type-search" data-name="search" data-type="search">
                <span class="facetwp-input-wrap">
                    <i class="facetwp-icon" data-ts="搜索">搜索</i>
                    <input type="text" class="facetwp-search" value="" placeholder="输入关键字" autocomplete="off" />
                </span>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="results-section">
            <div class="facetwp-template">
                <div class="row" id="articles-container">
                    <!--fwp-loop-->
                    <p>请输入搜索关键词开始搜索...</p>
                </div>
            </div>
        </div>

        <!-- 分页区域 -->
        <div class="pagination">
            <div class="facetwp-facet facetwp-facet-pagination facetwp-type-pager" data-name="pagination" data-type="pager">
                <div class="facetwp-pager"></div>
            </div>
        </div>
    </div>

    <!-- 加载JavaScript模块 -->
    <script type="module">
        // 导入搜索相关函数
        import { 
            setupSearchEvents, 
            handleSearchInput, 
            showLoading, 
            showError,
            renderArticleItems,
            setupPaginationEvents
        } from './js/render.js';
        
        import { searchArticlesByTitle } from './js/api.js';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成，初始化搜索功能...');
            
            // 设置搜索事件监听器
            setupSearchEvents();
            
            // 设置分页事件监听器
            setupPaginationEvents();
            
            // 暴露函数到全局作用域以便调试
            window.testSearch = async function(searchTerm) {
                try {
                    console.log('测试搜索:', searchTerm);
                    showLoading();
                    const result = await searchArticlesByTitle(searchTerm);
                    console.log('搜索结果:', result);
                    
                    if (result && result.articles && result.articles.length > 0) {
                        renderTestResults(result.articles);
                    } else {
                        showError(`没有找到包含"${searchTerm}"的文章`);
                    }
                } catch (error) {
                    console.error('搜索测试失败:', error);
                    showError('搜索失败: ' + error.message);
                }
            };
            
            // 简化的结果渲染函数
            function renderTestResults(articles) {
                const container = document.getElementById('articles-container');
                container.innerHTML = '<!--fwp-loop-->';
                
                articles.forEach(article => {
                    const articleDiv = document.createElement('div');
                    articleDiv.className = 'article-item';
                    articleDiv.innerHTML = `
                        <div class="article-title">${article.title || '无标题'}</div>
                        <div class="article-topic">主题: ${article.topic || '未分类'}</div>
                        <div class="article-url">链接: ${article.url || '#'}</div>
                    `;
                    container.appendChild(articleDiv);
                });
            }
            
            console.log('搜索功能初始化完成');
        });
    </script>
</body>
</html>
