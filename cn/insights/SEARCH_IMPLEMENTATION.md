# 搜索功能实现说明

## 概述
为 Crown RMS 中文网站的 insights 页面添加了基于标题的搜索功能。用户可以通过输入关键词来搜索相关文章。

## 实现的功能

### 1. 搜索接口
- **接口地址**: `/prod-api/api/web/doc/news/list`
- **搜索参数**: `title` - 用于按标题搜索文章
- **分页参数**: `pageNum`, `pageSize` (最大12条/页)
- **返回格式**: 标准的文章列表数据格式

### 2. 搜索交互
- **输入方式**: 
  - 在搜索框中输入关键词后按回车键
  - 点击搜索图标按钮
- **搜索状态**: 搜索时会显示加载状态
- **结果显示**: 搜索结果会替换当前的文章列表
- **分页支持**: 搜索结果支持分页浏览

### 3. 状态管理
- **搜索状态**: 记录当前搜索词，支持搜索结果的分页
- **主题筛选**: 搜索时会重置主题筛选状态
- **状态切换**: 支持在搜索、主题筛选、默认浏览之间切换

## 文件修改

### 1. `cn/insights/js/api.js`
添加了新的搜索函数：
- `searchArticlesByTitle(searchTerm, pageNum)` - 执行标题搜索

### 2. `cn/insights/js/render.js`
添加了搜索相关的功能：
- `setupSearchEvents()` - 设置搜索事件监听器
- `handleSearchInput(searchTerm)` - 处理搜索输入
- `performSearch(searchTerm, pageNum)` - 执行搜索操作
- `updatePaginationForSearch()` - 更新搜索结果的分页
- `resetTopicsDropdown()` - 重置主题下拉框
- `clearSearchInput()` - 清空搜索输入框
- 修改了 `handlePaginationClick()` 以支持搜索状态下的分页

### 3. 全局变量
- `currentSearchTerm` - 记录当前搜索词
- `currentSelectedTopic` - 记录当前选择的主题

## 使用方法

### 基本搜索
1. 在搜索框中输入关键词
2. 按回车键或点击搜索图标
3. 系统会显示包含该关键词的文章列表
4. 支持分页浏览搜索结果

### 清空搜索
1. 清空搜索框内容后按回车
2. 或者选择主题筛选
3. 系统会返回到默认的文章列表

### 状态优先级
1. **搜索状态** - 最高优先级，会覆盖主题筛选
2. **主题筛选** - 中等优先级，会清空搜索状态
3. **默认浏览** - 最低优先级，显示所有文章

## 测试

### 测试页面
创建了 `test-search.html` 用于测试搜索功能：
- 包含独立的搜索界面
- 可以直接测试搜索API
- 提供调试函数 `window.testSearch(searchTerm)`

### 测试方法
1. 打开 `cn/insights/test-search.html`
2. 在搜索框中输入测试关键词
3. 观察搜索结果和分页功能
4. 在浏览器控制台使用 `testSearch('关键词')` 进行调试

## 技术细节

### 搜索流程
1. 用户输入 → `handleSearchInput()` 
2. 调用 → `performSearch()` 
3. 请求 → `searchArticlesByTitle()` 
4. 渲染 → `renderArticleItems()` 
5. 分页 → `updatePaginationForSearch()`

### 分页处理
- 搜索状态下的分页会保持搜索词
- 主题筛选状态下的分页会保持主题选择
- 默认状态下使用原有的分页逻辑

### 错误处理
- 网络请求失败时显示错误信息
- 搜索无结果时显示提示信息
- 输入验证和参数校验

## 兼容性
- 保持与现有功能的完全兼容
- 不影响原有的主题筛选功能
- 不影响原有的分页功能
- 支持现代浏览器的ES6模块语法
